<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            width: 300px;
            padding: 15px;
            font-family: Arial, sans-serif;
            margin: 0;
        }
        
        h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            text-align: center;
        }
        
        button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        
        .test-btn {
            background-color: #2196F3;
            color: white;
        }
        
        .inject-btn {
            background-color: #FF9800;
            color: white;
        }
        
        .start-btn {
            background-color: #4CAF50;
            color: white;
        }
        
        .log {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            font-size: 11px;
            margin-top: 10px;
        }
        
        .log-entry {
            margin-bottom: 3px;
            color: #666;
        }
        
        input {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            box-sizing: border-box;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h2>🔧 调试版本</h2>
    
    <input type="text" id="selector" value="#selectItemBox > div > a.sure" placeholder="按钮选择器">
    
    <button class="inject-btn" onclick="injectScript()">1. 注入脚本</button>
    <button class="test-btn" onclick="testConnection()">2. 测试连接</button>
    <button class="test-btn" onclick="findElement()">3. 查找元素</button>
    <button class="start-btn" onclick="simpleClick()">4. 直接点击</button>
    
    <div class="log" id="log">
        <div class="log-entry">调试工具已加载...</div>
    </div>

    <script>
        function addLog(message) {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function injectScript() {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (tabs[0]) {
                    addLog(`正在向标签页 ${tabs[0].id} 注入脚本...`);
                    
                    chrome.scripting.executeScript({
                        target: { tabId: tabs[0].id },
                        func: function() {
                            // 直接在页面中定义函数
                            window.autoClickHelper = {
                                findElement: function(selector) {
                                    const element = document.querySelector(selector);
                                    if (element) {
                                        console.log('找到元素:', element);
                                        return true;
                                    } else {
                                        console.log('未找到元素:', selector);
                                        return false;
                                    }
                                },
                                clickElement: function(selector) {
                                    const element = document.querySelector(selector);
                                    if (element) {
                                        element.click();
                                        console.log('已点击元素');
                                        return true;
                                    } else {
                                        console.log('未找到要点击的元素');
                                        return false;
                                    }
                                }
                            };
                            
                            // 显示注入成功提示
                            const notification = document.createElement('div');
                            notification.style.cssText = `
                                position: fixed;
                                top: 20px;
                                right: 20px;
                                background: #4CAF50;
                                color: white;
                                padding: 10px 15px;
                                border-radius: 4px;
                                font-size: 14px;
                                z-index: 999999;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                            `;
                            notification.textContent = '脚本注入成功！';
                            document.body.appendChild(notification);
                            
                            setTimeout(() => {
                                if (notification.parentNode) {
                                    notification.parentNode.removeChild(notification);
                                }
                            }, 2000);
                            
                            return 'success';
                        }
                    }, function(result) {
                        if (chrome.runtime.lastError) {
                            addLog('注入失败: ' + chrome.runtime.lastError.message);
                        } else {
                            addLog('脚本注入成功！');
                        }
                    });
                }
            });
        }

        function testConnection() {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (tabs[0]) {
                    chrome.scripting.executeScript({
                        target: { tabId: tabs[0].id },
                        func: function() {
                            return window.autoClickHelper ? 'connected' : 'not_connected';
                        }
                    }, function(result) {
                        if (chrome.runtime.lastError) {
                            addLog('连接测试失败: ' + chrome.runtime.lastError.message);
                        } else if (result && result[0]) {
                            if (result[0].result === 'connected') {
                                addLog('✅ 连接正常');
                            } else {
                                addLog('❌ 未连接，请先注入脚本');
                            }
                        }
                    });
                }
            });
        }

        function findElement() {
            const selector = document.getElementById('selector').value;
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (tabs[0]) {
                    chrome.scripting.executeScript({
                        target: { tabId: tabs[0].id },
                        func: function(sel) {
                            if (window.autoClickHelper) {
                                return window.autoClickHelper.findElement(sel);
                            }
                            return false;
                        },
                        args: [selector]
                    }, function(result) {
                        if (chrome.runtime.lastError) {
                            addLog('查找失败: ' + chrome.runtime.lastError.message);
                        } else if (result && result[0]) {
                            if (result[0].result) {
                                addLog('✅ 找到元素: ' + selector);
                            } else {
                                addLog('❌ 未找到元素: ' + selector);
                            }
                        }
                    });
                }
            });
        }

        function simpleClick() {
            const selector = document.getElementById('selector').value;
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (tabs[0]) {
                    chrome.scripting.executeScript({
                        target: { tabId: tabs[0].id },
                        func: function(sel) {
                            if (window.autoClickHelper) {
                                return window.autoClickHelper.clickElement(sel);
                            }
                            return false;
                        },
                        args: [selector]
                    }, function(result) {
                        if (chrome.runtime.lastError) {
                            addLog('点击失败: ' + chrome.runtime.lastError.message);
                        } else if (result && result[0]) {
                            if (result[0].result) {
                                addLog('✅ 点击成功！');
                            } else {
                                addLog('❌ 点击失败，未找到元素');
                            }
                        }
                    });
                }
            });
        }
    </script>
</body>
</html>

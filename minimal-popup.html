<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { width: 300px; padding: 15px; font-family: Arial, sans-serif; }
        button { width: 100%; padding: 10px; margin: 5px 0; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
        #log { background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin-top: 10px; min-height: 150px; font-size: 12px; white-space: pre-wrap; }
        .status { background: #e7f3ff; padding: 5px; margin: 5px 0; font-size: 11px; }
    </style>
</head>
<body>
    <h3>🔧 最小调试版本</h3>
    
    <div class="status">
        状态: <span id="status">检查中...</span><br>
        时间: <span id="time"></span>
    </div>
    
    <button onclick="testBasic()">基础测试</button>
    <button onclick="testChrome()">Chrome API测试</button>
    <button onclick="testScript()">脚本注入测试</button>
    <button onclick="clickButton()">点击按钮测试</button>
    <button onclick="clearLog()">清空日志</button>
    
    <div id="log">
[页面加载] HTML已渲染
[等待] JavaScript执行...
    </div>

    <script>
        // 全局变量
        let logCount = 0;
        
        // 日志函数
        function addLog(msg) {
            logCount++;
            const time = new Date().toLocaleTimeString();
            const logEl = document.getElementById('log');
            logEl.textContent += `[${time}] ${msg}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[扩展-${logCount}] ${msg}`);
        }
        
        // 状态更新
        function updateStatus() {
            const statusEl = document.getElementById('status');
            const timeEl = document.getElementById('time');
            
            statusEl.textContent = typeof chrome !== 'undefined' ? '✅ 正常' : '❌ 异常';
            statusEl.style.color = typeof chrome !== 'undefined' ? 'green' : 'red';
            timeEl.textContent = new Date().toLocaleTimeString();
        }
        
        // 测试函数
        function testBasic() {
            addLog('=== 基础测试开始 ===');
            addLog('JavaScript: ✅ 正常');
            addLog('DOM操作: ✅ 正常');
            addLog('事件处理: ✅ 正常');
            addLog('Chrome对象: ' + (typeof chrome !== 'undefined' ? '✅ 存在' : '❌ 不存在'));
            
            if (typeof chrome !== 'undefined') {
                addLog('chrome.tabs: ' + (chrome.tabs ? '✅' : '❌'));
                addLog('chrome.scripting: ' + (chrome.scripting ? '✅' : '❌'));
                addLog('chrome.runtime: ' + (chrome.runtime ? '✅' : '❌'));
            }
            addLog('=== 基础测试完成 ===');
        }
        
        function testChrome() {
            addLog('=== Chrome API测试开始 ===');
            
            if (typeof chrome === 'undefined') {
                addLog('❌ Chrome API不可用');
                return;
            }
            
            try {
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    if (chrome.runtime.lastError) {
                        addLog('❌ 查询标签页失败: ' + chrome.runtime.lastError.message);
                    } else {
                        addLog('✅ 成功查询标签页');
                        addLog('标签页数量: ' + tabs.length);
                        if (tabs.length > 0) {
                            addLog('当前页面: ' + tabs[0].url);
                            addLog('页面标题: ' + tabs[0].title);
                        }
                    }
                    addLog('=== Chrome API测试完成 ===');
                });
            } catch (error) {
                addLog('❌ Chrome API测试异常: ' + error.message);
            }
        }
        
        function testScript() {
            addLog('=== 脚本注入测试开始 ===');
            
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (!tabs || tabs.length === 0) {
                    addLog('❌ 未找到活动标签页');
                    return;
                }
                
                const tabId = tabs[0].id;
                addLog('目标标签页ID: ' + tabId);
                
                chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    func: function() {
                        // 在目标页面执行的代码
                        return {
                            url: window.location.href,
                            title: document.title,
                            readyState: document.readyState,
                            timestamp: new Date().toISOString()
                        };
                    }
                }, function(results) {
                    if (chrome.runtime.lastError) {
                        addLog('❌ 脚本注入失败: ' + chrome.runtime.lastError.message);
                    } else if (results && results[0]) {
                        addLog('✅ 脚本注入成功');
                        const data = results[0].result;
                        addLog('页面URL: ' + data.url);
                        addLog('页面标题: ' + data.title);
                        addLog('页面状态: ' + data.readyState);
                    } else {
                        addLog('❌ 未收到执行结果');
                    }
                    addLog('=== 脚本注入测试完成 ===');
                });
            });
        }
        
        function clickButton() {
            addLog('=== 点击按钮测试开始 ===');
            const selector = '#selectItemBox > div > a.sure';
            addLog('目标选择器: ' + selector);
            
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (!tabs || tabs.length === 0) {
                    addLog('❌ 未找到活动标签页');
                    return;
                }
                
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    func: function(sel) {
                        // 查找元素
                        const element = document.querySelector(sel);
                        if (element) {
                            // 高亮显示
                            const originalStyle = element.style.cssText;
                            element.style.cssText += 'border: 3px solid red !important; background: yellow !important;';
                            
                            // 1秒后点击
                            setTimeout(() => {
                                element.style.cssText = originalStyle;
                                element.click();
                            }, 1000);
                            
                            return {
                                found: true,
                                tagName: element.tagName,
                                className: element.className,
                                text: element.textContent.trim().substring(0, 30)
                            };
                        } else {
                            return { found: false };
                        }
                    },
                    args: [selector]
                }, function(results) {
                    if (chrome.runtime.lastError) {
                        addLog('❌ 执行失败: ' + chrome.runtime.lastError.message);
                    } else if (results && results[0]) {
                        const result = results[0].result;
                        if (result.found) {
                            addLog('✅ 找到并点击元素');
                            addLog('标签: ' + result.tagName);
                            addLog('类名: ' + result.className);
                            addLog('文本: ' + result.text);
                        } else {
                            addLog('❌ 未找到元素');
                        }
                    }
                    addLog('=== 点击按钮测试完成 ===');
                });
            });
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            logCount = 0;
            addLog('日志已清空');
        }
        
        // 页面加载时立即执行
        document.addEventListener('DOMContentLoaded', function() {
            addLog('DOM加载完成');
            updateStatus();
            
            // 每秒更新状态
            setInterval(updateStatus, 1000);
            
            // 自动运行基础测试
            setTimeout(testBasic, 100);
        });
        
        // 立即执行的代码
        console.log('[扩展] 脚本开始执行');
        
        // 页面卸载前的清理
        window.addEventListener('beforeunload', function() {
            console.log('[扩展] 页面即将卸载');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            width: 320px;
            padding: 15px;
            font-family: Arial, sans-serif;
            margin: 0;
        }
        
        h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 12px;
        }
        
        label {
            display: block;
            margin-bottom: 4px;
            font-size: 12px;
            color: #555;
            font-weight: bold;
        }
        
        input, select {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            box-sizing: border-box;
        }
        
        button {
            width: 100%;
            padding: 8px;
            margin: 4px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }
        
        .start-btn {
            background-color: #4CAF50;
            color: white;
        }
        
        .start-btn:hover {
            background-color: #45a049;
        }
        
        .stop-btn {
            background-color: #f44336;
            color: white;
        }
        
        .stop-btn:hover {
            background-color: #da190b;
        }
        
        .test-btn {
            background-color: #2196F3;
            color: white;
        }
        
        .test-btn:hover {
            background-color: #0b7dda;
        }
        
        .status {
            padding: 8px;
            margin: 8px 0;
            border-radius: 4px;
            font-size: 11px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.running {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.stopped {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .preset-buttons {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
        }
        
        .preset-btn {
            flex: 1;
            padding: 4px;
            font-size: 10px;
            background-color: #e9ecef;
            color: #495057;
            border: 1px solid #ced4da;
        }
        
        .preset-btn:hover {
            background-color: #dee2e6;
        }
        
        .log {
            max-height: 80px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 6px;
            font-size: 10px;
            margin-top: 8px;
        }
        
        .log-entry {
            margin-bottom: 2px;
            color: #666;
        }
    </style>
</head>
<body>
    <h2>🖱️ 自动点击助手</h2>
    
    <div class="form-group">
        <label>按钮选择器:</label>
        <input type="text" id="selector" value="#selectItemBox > div > a.sure">
        <div class="preset-buttons">
            <button class="preset-btn" onclick="setPreset('确定')">确定按钮</button>
            <button class="preset-btn" onclick="setPreset('提交')">提交按钮</button>
            <button class="preset-btn" onclick="setPreset('购买')">购买按钮</button>
        </div>
    </div>
    
    <div class="form-group">
        <label>点击间隔 (秒):</label>
        <input type="number" id="interval" value="3" min="1" max="60">
    </div>
    
    <div class="form-group">
        <label>最大点击次数 (0=无限制):</label>
        <input type="number" id="maxClicks" value="0" min="0">
    </div>
    
    <button id="startBtn" class="start-btn" onclick="startAutoClick()">▶️ 开始自动点击</button>
    <button id="stopBtn" class="stop-btn" onclick="stopAutoClick()">⏹️ 停止点击</button>
    <button class="test-btn" onclick="testClick()">🧪 测试点击一次</button>
    
    <div id="status" class="status stopped">状态: 已停止</div>
    
    <div class="log" id="log">
        <div class="log-entry">插件已加载，等待操作...</div>
    </div>

    <script src="popup.js"></script>
</body>
</html>

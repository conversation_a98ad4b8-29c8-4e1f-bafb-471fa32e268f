<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        button {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            background-color: #4CAF50;
            color: white;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 15px;
            max-height: 150px;
            overflow-y: auto;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            color: #666;
        }
        
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        h3 {
            margin: 0 0 15px 0;
            text-align: center;
            color: #333;
        }
    </style>
</head>
<body>
    <h3>🖱️ 自动点击助手</h3>
    
    <input type="text" id="selector" value="#selectItemBox > div > a.sure" placeholder="按钮选择器">
    
    <button onclick="testBasicFunction()">测试基本功能</button>
    <button onclick="findAndClick()">查找并点击按钮</button>
    <button onclick="startAutoClick()">开始自动点击 (5秒间隔)</button>
    <button onclick="stopAutoClick()">停止自动点击</button>
    
    <div class="log" id="log">
        <div class="log-entry">扩展已加载，请点击测试按钮...</div>
    </div>

    <script>
        let autoClickTimer = null;
        
        function addLog(message) {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            
            // 限制日志条数
            if (log.children.length > 20) {
                log.removeChild(log.firstChild);
            }
        }
        
        function testBasicFunction() {
            addLog('开始测试基本功能...');
            
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (chrome.runtime.lastError) {
                    addLog('错误: ' + chrome.runtime.lastError.message);
                    return;
                }
                
                if (!tabs || tabs.length === 0) {
                    addLog('错误: 未找到活动标签页');
                    return;
                }
                
                addLog('✅ 找到活动标签页: ' + tabs[0].url);
                
                // 测试脚本注入
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    func: function() {
                        return {
                            url: window.location.href,
                            title: document.title,
                            readyState: document.readyState
                        };
                    }
                }, function(results) {
                    if (chrome.runtime.lastError) {
                        addLog('脚本注入失败: ' + chrome.runtime.lastError.message);
                    } else if (results && results[0]) {
                        const result = results[0].result;
                        addLog('✅ 脚本注入成功');
                        addLog('页面标题: ' + result.title);
                        addLog('页面状态: ' + result.readyState);
                    } else {
                        addLog('❌ 未收到脚本执行结果');
                    }
                });
            });
        }
        
        function findAndClick() {
            const selector = document.getElementById('selector').value;
            addLog('查找元素: ' + selector);
            
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (!tabs || tabs.length === 0) {
                    addLog('错误: 未找到活动标签页');
                    return;
                }
                
                chrome.scripting.executeScript({
                    target: { tabId: tabs[0].id },
                    func: function(sel) {
                        // 查找元素
                        const element = document.querySelector(sel);
                        if (element) {
                            // 滚动到元素位置
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            
                            // 高亮显示元素
                            const originalStyle = element.style.cssText;
                            element.style.cssText += 'border: 3px solid red !important; background-color: yellow !important;';
                            
                            setTimeout(() => {
                                element.style.cssText = originalStyle;
                                // 点击元素
                                element.click();
                            }, 1000);
                            
                            return {
                                found: true,
                                tagName: element.tagName,
                                className: element.className,
                                text: element.textContent.trim().substring(0, 50)
                            };
                        } else {
                            return { found: false };
                        }
                    },
                    args: [selector]
                }, function(results) {
                    if (chrome.runtime.lastError) {
                        addLog('执行失败: ' + chrome.runtime.lastError.message);
                    } else if (results && results[0]) {
                        const result = results[0].result;
                        if (result.found) {
                            addLog('✅ 找到元素并点击');
                            addLog('标签: ' + result.tagName);
                            addLog('类名: ' + result.className);
                            addLog('文本: ' + result.text);
                        } else {
                            addLog('❌ 未找到元素: ' + selector);
                        }
                    }
                });
            });
        }
        
        function startAutoClick() {
            if (autoClickTimer) {
                addLog('自动点击已在运行中');
                return;
            }
            
            addLog('开始自动点击，间隔5秒...');
            
            // 立即执行一次
            findAndClick();
            
            // 设置定时器
            autoClickTimer = setInterval(() => {
                findAndClick();
            }, 5000);
        }
        
        function stopAutoClick() {
            if (autoClickTimer) {
                clearInterval(autoClickTimer);
                autoClickTimer = null;
                addLog('✅ 自动点击已停止');
            } else {
                addLog('自动点击未在运行');
            }
        }
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoClickTimer) {
                clearInterval(autoClickTimer);
            }
        });
    </script>
</body>
</html>

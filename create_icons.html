<!DOCTYPE html>
<html>
<head>
    <title>生成扩展图标</title>
</head>
<body>
    <h2>扩展图标生成器</h2>
    <p>请在浏览器中打开此文件，然后右键保存下面的图标为PNG文件</p>
    
    <h3>16x16 图标</h3>
    <canvas id="icon16" width="16" height="16" style="border: 1px solid #ccc; image-rendering: pixelated; width: 64px; height: 64px;"></canvas>
    
    <h3>48x48 图标</h3>
    <canvas id="icon48" width="48" height="48" style="border: 1px solid #ccc; width: 96px; height: 96px;"></canvas>
    
    <h3>128x128 图标</h3>
    <canvas id="icon128" width="128" height="128" style="border: 1px solid #ccc; width: 128px; height: 128px;"></canvas>
    
    <div style="margin-top: 20px;">
        <button onclick="downloadIcon('icon16', 'icon16.png')">下载 16x16 图标</button>
        <button onclick="downloadIcon('icon48', 'icon48.png')">下载 48x48 图标</button>
        <button onclick="downloadIcon('icon128', 'icon128.png')">下载 128x128 图标</button>
    </div>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(0, 0, size, size);
            
            // 鼠标指针
            const scale = size / 48;
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.moveTo(8 * scale, 8 * scale);
            ctx.lineTo(8 * scale, 32 * scale);
            ctx.lineTo(14 * scale, 26 * scale);
            ctx.lineTo(20 * scale, 32 * scale);
            ctx.lineTo(24 * scale, 28 * scale);
            ctx.lineTo(18 * scale, 22 * scale);
            ctx.lineTo(24 * scale, 16 * scale);
            ctx.closePath();
            ctx.fill();
            
            // 点击效果圆圈
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2 * scale;
            ctx.beginPath();
            ctx.arc(32 * scale, 16 * scale, 8 * scale, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 小圆点
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(32 * scale, 16 * scale, 2 * scale, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 绘制所有图标
        drawIcon(document.getElementById('icon16'), 16);
        drawIcon(document.getElementById('icon48'), 48);
        drawIcon(document.getElementById('icon128'), 128);
    </script>
</body>
</html>

# 自动点击助手 Chrome 扩展

一个简单易用的浏览器扩展，可以定时自动点击网页上的指定按钮。

## 功能特性

- 🖱️ 自动点击指定的网页元素
- ⏰ 可设置点击间隔时间（1-60秒）
- 🔢 可设置最大点击次数
- 🧪 测试点击功能
- 📊 实时状态显示和操作日志
- 💾 自动保存设置
- 🎯 预设常用按钮选择器

## 安装方法

### 1. 下载扩展文件
确保你有以下文件：
- `manifest.json`
- `popup.html`
- `popup.js`
- `content.js`
- `README.md`

### 2. 安装到Chrome浏览器

1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 打开右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择包含所有扩展文件的文件夹
6. 扩展安装完成！

### 3. 使用方法

1. **准备阶段**：
   - 打开需要自动点击的网页
   - 进行到需要点击确定按钮的步骤

2. **启动扩展**：
   - 点击浏览器工具栏中的扩展图标
   - 在弹出的面板中设置参数

3. **开始自动点击**：
   - 确认按钮选择器（默认已设置为你的确定按钮）
   - 设置点击间隔时间
   - 点击"开始自动点击"

## 使用说明

### 按钮选择器
- 默认设置：`#selectItemBox > div > a.sure`（你的确定按钮）
- 可以使用预设按钮快速设置
- 也可以手动输入CSS选择器

### 点击间隔
- 建议设置3-10秒，避免过于频繁
- 最小1秒，最大60秒

### 最大点击次数
- 设置为0表示无限制点击
- 设置具体数字可以自动停止

### 操作按钮
- **开始自动点击**：启动定时点击
- **停止点击**：立即停止自动点击
- **测试点击一次**：验证按钮是否能正确找到和点击

## 注意事项

1. **使用前准备**：确保目标网页已经加载完成，确定按钮已显示
2. **合理设置间隔**：避免过于频繁的点击，建议3秒以上
3. **及时停止**：完成操作后记得停止自动点击
4. **兼容性**：支持大部分网页，包括iframe中的元素

## 故障排除

### 找不到按钮元素
- 检查按钮选择器是否正确
- 确认按钮已经显示在页面上
- 尝试使用"测试点击一次"功能验证

### 点击无效果
- 某些网页可能有特殊的点击保护
- 尝试增加点击间隔时间
- 检查是否有其他弹窗阻挡

### 扩展无响应
- 刷新目标网页
- 重新打开扩展面板
- 检查浏览器控制台是否有错误信息

## 技术说明

- 使用Chrome Extension Manifest V3
- 支持跨iframe元素查找
- 模拟真实的鼠标点击事件
- 自动保存用户设置

## 安全提示

- 仅在你信任的网站上使用
- 不要设置过短的点击间隔
- 使用完毕后及时停止自动点击

// 自动点击状态
let autoClickInterval = null;
let clickCount = 0;
let maxClicks = 0;
let currentSelector = '';

// 执行点击操作
function performClick(selector) {
    // 尝试多种方式查找元素
    let button = null;
    
    // 1. 直接查找
    button = document.querySelector(selector);
    
    // 2. 如果没找到，尝试在所有iframe中查找
    if (!button) {
        const iframes = document.querySelectorAll('iframe');
        for (let iframe of iframes) {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                button = iframeDoc.querySelector(selector);
                if (button) break;
            } catch (e) {
                // 跨域iframe无法访问，忽略错误
                console.log('无法访问iframe:', e.message);
            }
        }
    }
    
    // 3. 如果还没找到，尝试等待元素出现
    if (!button) {
        // 尝试等待元素出现（最多等待2秒）
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 20; // 2秒 / 100ms = 20次
            
            const checkElement = () => {
                button = document.querySelector(selector);
                attempts++;
                
                if (button || attempts >= maxAttempts) {
                    if (button) {
                        clickElement(button);
                        resolve({ success: true, message: `成功点击按钮 (第 ${++clickCount} 次)` });
                    } else {
                        resolve({ success: false, message: `未找到按钮元素: ${selector}` });
                    }
                } else {
                    setTimeout(checkElement, 100);
                }
            };
            
            checkElement();
        });
    } else {
        clickElement(button);
        return Promise.resolve({ success: true, message: `成功点击按钮 (第 ${++clickCount} 次)` });
    }
}

// 点击元素
function clickElement(element) {
    // 确保元素可见
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // 模拟真实的点击事件
    const events = ['mousedown', 'mouseup', 'click'];
    
    events.forEach(eventType => {
        const event = new MouseEvent(eventType, {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: element.getBoundingClientRect().left + element.offsetWidth / 2,
            clientY: element.getBoundingClientRect().top + element.offsetHeight / 2
        });
        element.dispatchEvent(event);
    });
    
    // 如果是链接或按钮，也触发focus事件
    if (element.tagName === 'A' || element.tagName === 'BUTTON' || element.type === 'submit') {
        element.focus();
    }
}

// 开始自动点击
function startAutoClick(selector, interval, maxClicksLimit) {
    if (autoClickInterval) {
        clearInterval(autoClickInterval);
    }
    
    currentSelector = selector;
    maxClicks = maxClicksLimit;
    clickCount = 0;
    
    // 立即执行一次
    performClick(selector).then(result => {
        sendStatusUpdate(true, result.message);
        
        // 检查是否达到最大次数
        if (maxClicks > 0 && clickCount >= maxClicks) {
            stopAutoClick();
            sendStatusUpdate(false, `已达到最大点击次数 ${maxClicks}，自动停止`);
            return;
        }
        
        // 设置定时器
        autoClickInterval = setInterval(async () => {
            const result = await performClick(selector);
            sendStatusUpdate(true, result.message);
            
            // 检查是否达到最大次数
            if (maxClicks > 0 && clickCount >= maxClicks) {
                stopAutoClick();
                sendStatusUpdate(false, `已达到最大点击次数 ${maxClicks}，自动停止`);
            }
        }, interval * 1000);
    });
}

// 停止自动点击
function stopAutoClick() {
    if (autoClickInterval) {
        clearInterval(autoClickInterval);
        autoClickInterval = null;
        sendStatusUpdate(false, '自动点击已停止');
    }
}

// 发送状态更新到popup
function sendStatusUpdate(isRunning, message) {
    chrome.runtime.sendMessage({
        action: 'updateStatus',
        isRunning: isRunning,
        clickCount: clickCount,
        message: message
    });
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    switch(request.action) {
        case 'start':
            startAutoClick(request.selector, request.interval, request.maxClicks);
            sendResponse({ 
                message: `开始自动点击，间隔 ${request.interval} 秒`,
                status: 'running',
                clickCount: clickCount
            });
            break;
            
        case 'stop':
            stopAutoClick();
            sendResponse({ 
                message: '自动点击已停止',
                status: 'stopped',
                clickCount: clickCount
            });
            break;
            
        case 'test':
            performClick(request.selector).then(result => {
                sendResponse({ 
                    message: result.message,
                    status: autoClickInterval ? 'running' : 'stopped',
                    clickCount: clickCount
                });
            });
            return true; // 保持消息通道开放
            
        case 'getStatus':
            sendResponse({
                message: autoClickInterval ? '自动点击运行中' : '自动点击已停止',
                status: autoClickInterval ? 'running' : 'stopped',
                clickCount: clickCount
            });
            break;
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (autoClickInterval) {
        clearInterval(autoClickInterval);
    }
});

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('自动点击助手已加载');
    });
} else {
    console.log('自动点击助手已加载');
}

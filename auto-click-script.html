<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时点击确定按钮脚本</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .control-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .stop-btn {
            background-color: #dc3545;
        }
        .stop-btn:hover {
            background-color: #c82333;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.running {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.stopped {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
        }
        .log-entry {
            margin-bottom: 5px;
            font-size: 12px;
            color: #666;
        }
        .script-code {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>定时点击确定按钮脚本</h1>
        
        <div class="control-group">
            <label for="selector">按钮选择器:</label>
            <input type="text" id="selector" value="#selectItemBox > div > a.sure" placeholder="CSS选择器">
        </div>
        
        <div class="control-group">
            <label for="interval">点击间隔 (秒):</label>
            <input type="number" id="interval" value="5" min="1" max="3600">
        </div>
        
        <div class="control-group">
            <label for="maxClicks">最大点击次数 (0表示无限制):</label>
            <input type="number" id="maxClicks" value="0" min="0">
        </div>
        
        <div class="control-group">
            <button id="startBtn" onclick="startAutoClick()">开始自动点击</button>
            <button id="stopBtn" onclick="stopAutoClick()" class="stop-btn" disabled>停止点击</button>
            <button onclick="testClick()">测试点击一次</button>
        </div>
        
        <div id="status" class="status stopped">状态: 已停止</div>
        
        <div class="log" id="log">
            <div class="log-entry">脚本已加载，等待开始...</div>
        </div>
        
        <div class="script-code">
            <strong>使用说明:</strong><br>
            1. 确保目标页面已打开<br>
            2. 修改按钮选择器（如果需要）<br>
            3. 设置点击间隔时间<br>
            4. 点击"开始自动点击"按钮<br>
            5. 可以随时点击"停止点击"来停止<br><br>
            
            <strong>控制台脚本版本:</strong><br>
            如果你想直接在浏览器控制台运行，可以复制以下代码：<br><br>
            
            // 定时点击脚本
            let autoClickInterval;
            let clickCount = 0;
            
            function autoClick() {
                const button = document.querySelector("#selectItemBox > div > a.sure");
                if (button) {
                    button.click();
                    clickCount++;
                    console.log(`已点击 ${clickCount} 次 - ${new Date().toLocaleTimeString()}`);
                } else {
                    console.log("未找到按钮元素");
                }
            }
            
            function startAutoClick(intervalSeconds = 5) {
                if (autoClickInterval) {
                    clearInterval(autoClickInterval);
                }
                autoClickInterval = setInterval(autoClick, intervalSeconds * 1000);
                console.log(`自动点击已开始，间隔 ${intervalSeconds} 秒`);
            }
            
            function stopAutoClick() {
                if (autoClickInterval) {
                    clearInterval(autoClickInterval);
                    autoClickInterval = null;
                    console.log("自动点击已停止");
                }
            }
            
            // 开始自动点击（间隔5秒）
            startAutoClick(5);
        </div>
    </div>

    <script>
        let autoClickInterval = null;
        let clickCount = 0;
        let maxClicks = 0;

        function addLog(message) {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function updateStatus(isRunning) {
            const status = document.getElementById('status');
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            
            if (isRunning) {
                status.textContent = `状态: 运行中 (已点击 ${clickCount} 次)`;
                status.className = 'status running';
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                status.textContent = `状态: 已停止 (总共点击 ${clickCount} 次)`;
                status.className = 'status stopped';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        function performClick() {
            const selector = document.getElementById('selector').value;
            
            // 尝试在当前页面查找按钮
            let button = document.querySelector(selector);
            
            // 如果当前页面没找到，尝试在所有iframe中查找
            if (!button) {
                const iframes = document.querySelectorAll('iframe');
                for (let iframe of iframes) {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        button = iframeDoc.querySelector(selector);
                        if (button) break;
                    } catch (e) {
                        // 跨域iframe无法访问，忽略错误
                    }
                }
            }
            
            if (button) {
                // 模拟真实的点击事件
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                button.dispatchEvent(clickEvent);
                
                clickCount++;
                addLog(`成功点击按钮 (第 ${clickCount} 次)`);
                updateStatus(true);
                
                // 检查是否达到最大点击次数
                if (maxClicks > 0 && clickCount >= maxClicks) {
                    stopAutoClick();
                    addLog(`已达到最大点击次数 ${maxClicks}，自动停止`);
                }
                
                return true;
            } else {
                addLog(`未找到按钮元素: ${selector}`);
                return false;
            }
        }

        function startAutoClick() {
            const interval = parseInt(document.getElementById('interval').value);
            maxClicks = parseInt(document.getElementById('maxClicks').value);
            
            if (interval < 1) {
                alert('点击间隔必须大于0秒');
                return;
            }
            
            if (autoClickInterval) {
                clearInterval(autoClickInterval);
            }
            
            clickCount = 0;
            addLog(`开始自动点击，间隔 ${interval} 秒${maxClicks > 0 ? `，最大次数 ${maxClicks}` : ''}`);
            
            // 立即执行一次
            performClick();
            
            // 设置定时器
            autoClickInterval = setInterval(() => {
                performClick();
            }, interval * 1000);
            
            updateStatus(true);
        }

        function stopAutoClick() {
            if (autoClickInterval) {
                clearInterval(autoClickInterval);
                autoClickInterval = null;
                addLog('自动点击已停止');
                updateStatus(false);
            }
        }

        function testClick() {
            addLog('执行测试点击...');
            performClick();
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoClickInterval) {
                clearInterval(autoClickInterval);
            }
        });
    </script>
</body>
</html>

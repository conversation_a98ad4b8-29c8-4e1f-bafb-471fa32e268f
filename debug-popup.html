<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            width: 250px;
            padding: 15px;
            font-family: Arial, sans-serif;
        }
        
        button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background-color: #4CAF50;
            color: white;
        }
        
        #output {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 10px;
            min-height: 100px;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h3>调试测试</h3>
    
    <button onclick="test1()">测试1: 基础功能</button>
    <button onclick="test2()">测试2: Chrome API</button>
    <button onclick="test3()">测试3: 脚本注入</button>
    <button onclick="clearOutput()">清空输出</button>
    
    <div id="output">等待测试...</div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            const time = new Date().toLocaleTimeString();
            output.textContent += `[${time}] ${message}\n`;
            console.log(message);
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
            log('输出已清空');
        }
        
        function test1() {
            log('开始测试1: 基础功能');
            
            try {
                log('✅ JavaScript正常运行');
                log('✅ DOM操作正常');
                log('✅ 事件处理正常');
                
                // 测试Chrome扩展API是否可用
                if (typeof chrome !== 'undefined') {
                    log('✅ Chrome API可用');
                } else {
                    log('❌ Chrome API不可用');
                }
                
            } catch (error) {
                log('❌ 测试1失败: ' + error.message);
            }
        }
        
        function test2() {
            log('开始测试2: Chrome API');
            
            try {
                if (typeof chrome === 'undefined') {
                    log('❌ Chrome对象不存在');
                    return;
                }
                
                if (chrome.tabs) {
                    log('✅ chrome.tabs API可用');
                } else {
                    log('❌ chrome.tabs API不可用');
                }
                
                if (chrome.scripting) {
                    log('✅ chrome.scripting API可用');
                } else {
                    log('❌ chrome.scripting API不可用');
                }
                
                // 尝试获取当前标签页
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    if (chrome.runtime.lastError) {
                        log('❌ 获取标签页失败: ' + chrome.runtime.lastError.message);
                    } else if (tabs && tabs.length > 0) {
                        log('✅ 成功获取当前标签页');
                        log('标签页ID: ' + tabs[0].id);
                        log('标签页URL: ' + tabs[0].url);
                    } else {
                        log('❌ 未找到活动标签页');
                    }
                });
                
            } catch (error) {
                log('❌ 测试2失败: ' + error.message);
            }
        }
        
        function test3() {
            log('开始测试3: 脚本注入');
            
            try {
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    if (chrome.runtime.lastError) {
                        log('❌ 查询标签页失败: ' + chrome.runtime.lastError.message);
                        return;
                    }
                    
                    if (!tabs || tabs.length === 0) {
                        log('❌ 未找到活动标签页');
                        return;
                    }
                    
                    const tabId = tabs[0].id;
                    log('尝试向标签页 ' + tabId + ' 注入脚本...');
                    
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        func: function() {
                            return {
                                url: window.location.href,
                                title: document.title,
                                timestamp: new Date().toISOString()
                            };
                        }
                    }, function(results) {
                        if (chrome.runtime.lastError) {
                            log('❌ 脚本注入失败: ' + chrome.runtime.lastError.message);
                        } else if (results && results[0]) {
                            log('✅ 脚本注入成功!');
                            const result = results[0].result;
                            log('页面URL: ' + result.url);
                            log('页面标题: ' + result.title);
                            log('执行时间: ' + result.timestamp);
                        } else {
                            log('❌ 未收到脚本执行结果');
                        }
                    });
                });
                
            } catch (error) {
                log('❌ 测试3失败: ' + error.message);
            }
        }
        
        // 页面加载完成后自动运行测试1
        document.addEventListener('DOMContentLoaded', function() {
            log('调试页面已加载');
            setTimeout(test1, 100);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            width: 250px;
            padding: 15px;
            font-family: Arial, sans-serif;
        }
        
        button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background-color: #4CAF50;
            color: white;
        }
        
        #output {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 10px;
            min-height: 100px;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h3>调试测试</h3>
    
    <button onclick="test1()">测试1: 基础功能</button>
    <button onclick="test2()">测试2: Chrome API</button>
    <button onclick="test3()">测试3: 脚本注入</button>
    <button onclick="clearOutput()">清空输出</button>
    
    <div id="output">
[初始化] 页面HTML已加载
[初始化] 等待JavaScript执行...
    </div>

    <!-- 立即显示的状态信息 -->
    <div style="background-color: #e7f3ff; padding: 5px; margin-top: 5px; font-size: 11px;">
        <div>扩展状态: <span id="status">检查中...</span></div>
        <div>时间: <span id="time"></span></div>
    </div>

    <script>
        // 立即更新状态
        function updateStatus() {
            try {
                const statusEl = document.getElementById('status');
                const timeEl = document.getElementById('time');

                if (statusEl) {
                    statusEl.textContent = typeof chrome !== 'undefined' ? 'Chrome API可用' : 'Chrome API不可用';
                    statusEl.style.color = typeof chrome !== 'undefined' ? 'green' : 'red';
                }

                if (timeEl) {
                    timeEl.textContent = new Date().toLocaleTimeString();
                }
            } catch (e) {
                console.error('状态更新失败:', e);
            }
        }

        // 立即显示脚本开始执行
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('output').textContent = '[脚本开始执行] DOM已加载\n';
            updateStatus();

            // 每秒更新时间
            setInterval(updateStatus, 1000);
        });

        function log(message) {
            try {
                const output = document.getElementById('output');
                const time = new Date().toLocaleTimeString();
                const logMessage = `[${time}] ${message}\n`;

                if (output) {
                    output.textContent += logMessage;
                } else {
                    // 如果找不到output元素，直接在页面上显示
                    document.body.innerHTML += '<div style="color:red;">' + logMessage + '</div>';
                }

                console.log('[扩展日志] ' + message);

                // 同时在页面标题中显示最新消息
                document.title = '调试: ' + message.substring(0, 20);

            } catch (error) {
                console.error('[日志错误] ' + error.message);
                alert('日志错误: ' + error.message);
            }
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
            log('输出已清空');
        }
        
        function test1() {
            // 立即显示开始信息
            alert('测试1开始执行');
            log('开始测试1: 基础功能');

            try {
                log('✅ JavaScript正常运行');
                log('✅ DOM操作正常');
                log('✅ 事件处理正常');

                // 测试Chrome扩展API是否可用
                if (typeof chrome !== 'undefined') {
                    log('✅ Chrome API可用');
                    log('Chrome版本: ' + (chrome.runtime ? chrome.runtime.getManifest().version : '未知'));
                } else {
                    log('❌ Chrome API不可用');
                    alert('Chrome API不可用!');
                }

                // 测试各种API
                log('测试chrome.tabs: ' + (chrome.tabs ? '可用' : '不可用'));
                log('测试chrome.scripting: ' + (chrome.scripting ? '可用' : '不可用'));
                log('测试chrome.runtime: ' + (chrome.runtime ? '可用' : '不可用'));

            } catch (error) {
                log('❌ 测试1失败: ' + error.message);
                alert('测试1失败: ' + error.message);
            }
        }
        
        function test2() {
            log('开始测试2: Chrome API');
            
            try {
                if (typeof chrome === 'undefined') {
                    log('❌ Chrome对象不存在');
                    return;
                }
                
                if (chrome.tabs) {
                    log('✅ chrome.tabs API可用');
                } else {
                    log('❌ chrome.tabs API不可用');
                }
                
                if (chrome.scripting) {
                    log('✅ chrome.scripting API可用');
                } else {
                    log('❌ chrome.scripting API不可用');
                }
                
                // 尝试获取当前标签页
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    if (chrome.runtime.lastError) {
                        log('❌ 获取标签页失败: ' + chrome.runtime.lastError.message);
                    } else if (tabs && tabs.length > 0) {
                        log('✅ 成功获取当前标签页');
                        log('标签页ID: ' + tabs[0].id);
                        log('标签页URL: ' + tabs[0].url);
                    } else {
                        log('❌ 未找到活动标签页');
                    }
                });
                
            } catch (error) {
                log('❌ 测试2失败: ' + error.message);
            }
        }
        
        function test3() {
            log('开始测试3: 脚本注入');
            
            try {
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    if (chrome.runtime.lastError) {
                        log('❌ 查询标签页失败: ' + chrome.runtime.lastError.message);
                        return;
                    }
                    
                    if (!tabs || tabs.length === 0) {
                        log('❌ 未找到活动标签页');
                        return;
                    }
                    
                    const tabId = tabs[0].id;
                    log('尝试向标签页 ' + tabId + ' 注入脚本...');
                    
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        func: function() {
                            return {
                                url: window.location.href,
                                title: document.title,
                                timestamp: new Date().toISOString()
                            };
                        }
                    }, function(results) {
                        if (chrome.runtime.lastError) {
                            log('❌ 脚本注入失败: ' + chrome.runtime.lastError.message);
                        } else if (results && results[0]) {
                            log('✅ 脚本注入成功!');
                            const result = results[0].result;
                            log('页面URL: ' + result.url);
                            log('页面标题: ' + result.title);
                            log('执行时间: ' + result.timestamp);
                        } else {
                            log('❌ 未收到脚本执行结果');
                        }
                    });
                });
                
            } catch (error) {
                log('❌ 测试3失败: ' + error.message);
            }
        }
        
        // 立即执行的代码
        console.log('[扩展] 脚本开始加载');

        // 页面加载完成后自动运行测试1
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[扩展] DOM加载完成');
            log('调试页面已加载');

            // 立即显示一些基本信息
            log('当前时间: ' + new Date().toLocaleString());
            log('用户代理: ' + navigator.userAgent.substring(0, 50) + '...');
            log('页面URL: ' + window.location.href);

            // 延迟执行测试1
            setTimeout(function() {
                log('准备执行测试1...');
                test1();
            }, 500);
        });

        // 立即执行的测试
        try {
            console.log('[扩展] 立即测试开始');
            if (typeof chrome !== 'undefined') {
                console.log('[扩展] Chrome API可用');
            } else {
                console.log('[扩展] Chrome API不可用');
            }
        } catch (e) {
            console.error('[扩展] 立即测试失败:', e);
        }
    </script>
</body>
</html>

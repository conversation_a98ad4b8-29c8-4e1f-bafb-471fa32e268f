// 预设选择器
const presets = {
    '确定': [
        '#selectItemBox > div > a.sure',
        '.sure',
        '.confirm',
        '.ok-btn',
        'button[type="submit"]'
    ],
    '提交': [
        'button[type="submit"]',
        '.submit-btn',
        '.submit',
        'input[type="submit"]'
    ],
    '购买': [
        '.buy-btn',
        '.purchase-btn',
        '.add-to-cart',
        '.buy-now'
    ]
};

// 添加日志
function addLog(message) {
    const log = document.getElementById('log');
    const entry = document.createElement('div');
    entry.className = 'log-entry';
    entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
    log.appendChild(entry);
    log.scrollTop = log.scrollHeight;
    
    // 限制日志条数
    if (log.children.length > 20) {
        log.removeChild(log.firstChild);
    }
}

// 更新状态
function updateStatus(isRunning, clickCount = 0) {
    const status = document.getElementById('status');
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    
    if (isRunning) {
        status.textContent = `状态: 运行中 (已点击 ${clickCount} 次)`;
        status.className = 'status running';
        startBtn.disabled = true;
        stopBtn.disabled = false;
    } else {
        status.textContent = `状态: 已停止 (总共点击 ${clickCount} 次)`;
        status.className = 'status stopped';
        startBtn.disabled = false;
        stopBtn.disabled = true;
    }
}

// 设置预设选择器
function setPreset(type) {
    const selector = document.getElementById('selector');
    if (presets[type] && presets[type].length > 0) {
        selector.value = presets[type][0];
        addLog(`已设置为${type}按钮预设`);
    }
}

// 向内容脚本发送消息
function sendMessage(action, data = {}) {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: action,
                ...data
            }, function(response) {
                if (chrome.runtime.lastError) {
                    addLog('错误: ' + chrome.runtime.lastError.message);
                } else if (response) {
                    addLog(response.message);
                    if (response.status) {
                        updateStatus(response.status === 'running', response.clickCount || 0);
                    }
                }
            });
        }
    });
}

// 开始自动点击
function startAutoClick() {
    const selector = document.getElementById('selector').value;
    const interval = parseInt(document.getElementById('interval').value);
    const maxClicks = parseInt(document.getElementById('maxClicks').value);
    
    if (!selector.trim()) {
        addLog('请输入按钮选择器');
        return;
    }
    
    if (interval < 1) {
        addLog('点击间隔必须大于0秒');
        return;
    }
    
    sendMessage('start', {
        selector: selector,
        interval: interval,
        maxClicks: maxClicks
    });
    
    addLog(`开始自动点击，间隔 ${interval} 秒`);
}

// 停止自动点击
function stopAutoClick() {
    sendMessage('stop');
    addLog('发送停止指令');
}

// 测试点击
function testClick() {
    const selector = document.getElementById('selector').value;
    if (!selector.trim()) {
        addLog('请输入按钮选择器');
        return;
    }
    
    sendMessage('test', { selector: selector });
    addLog('执行测试点击');
}

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'updateStatus') {
        updateStatus(request.isRunning, request.clickCount);
        addLog(request.message);
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 恢复保存的设置
    chrome.storage.local.get(['selector', 'interval', 'maxClicks'], function(result) {
        if (result.selector) {
            document.getElementById('selector').value = result.selector;
        }
        if (result.interval) {
            document.getElementById('interval').value = result.interval;
        }
        if (result.maxClicks !== undefined) {
            document.getElementById('maxClicks').value = result.maxClicks;
        }
    });
    
    // 保存设置
    document.getElementById('selector').addEventListener('change', function() {
        chrome.storage.local.set({selector: this.value});
    });
    
    document.getElementById('interval').addEventListener('change', function() {
        chrome.storage.local.set({interval: this.value});
    });
    
    document.getElementById('maxClicks').addEventListener('change', function() {
        chrome.storage.local.set({maxClicks: this.value});
    });
    
    // 检查当前页面状态
    sendMessage('getStatus');
});
